/* Card Header Component Styles */
.card-header {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-secondary);
  background: var(--bg-card); /* Match card background */
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-md);
  width: 100%;
}

.header-text {
  flex: 1;
  min-width: 0; /* Allow text to shrink if needed */
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.2;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-start;
  flex-shrink: 0; /* Prevent actions from shrinking */
}

/* Ensure projected buttons follow the standardized styling */
.header-actions ::ng-deep .add-user-btn,
.header-actions ::ng-deep .submit-btn,
.header-actions ::ng-deep .cancel-btn,
.header-actions ::ng-deep .edit-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;
  background: #000000 !important; /* Black background */
  color: #ffffff !important; /* White text */
  border: 2px solid #ffffff !important; /* White thick border */

  mat-icon {
    margin-right: var(--spacing-xs);
    color: #ffffff !important;
  }

  &:hover {
    background: #333333 !important; /* Slightly lighter on hover */
    border-color: #ffffff !important;
  }
}

/* Cancel button specific styling - different from primary buttons */
.header-actions ::ng-deep .cancel-btn {
  background: transparent !important;
  color: var(--text-secondary) !important;
  border: 2px solid var(--border-secondary) !important;

  &:hover {
    background: var(--bg-hover) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-primary) !important;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .header-actions {
    justify-content: flex-end;
    width: 100%;
  }

  .page-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: var(--spacing-md);
  }

  .header-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .header-actions ::ng-deep button {
    width: 100%;
    justify-content: center;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .page-subtitle {
    font-size: 0.875rem;
  }
}

/* Dark Theme Overrides */
:host-context(.dark-theme) {
  .card-header {
    background: #2a2a2a; /* Greyish background for dark mode */
    border-bottom-color: #404040;
  }

  .page-title {
    color: #ffffff;
  }

  .page-subtitle {
    color: #b0b0b0;
  }
}
