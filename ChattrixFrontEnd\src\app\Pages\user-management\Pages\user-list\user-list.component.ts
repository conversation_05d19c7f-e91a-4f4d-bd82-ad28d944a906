import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  ViewChild,
  Injector,
} from '@angular/core';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';

import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';

import { UserManagementService } from '../../Services/UserManagement.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import { UserProfileService } from '../../../chattrix/Services/UserProfile.service';
import { AuthenticationService } from '../../../authentication/Services/Authentication.service';
import { AuthStateService } from '../../../authentication/Services/AuthState.service';

import {
  UserDetails,
  PaginationParameters,
  UserFilterOptions,
  ModalState,
  SortConfig,
  LoadingStates,
  ErrorStates,
  UserTableRow,
  TableColumn,
  UserActionPermissions,
} from '../../Models/UserManagement';

@Component({
  selector: 'app-user-list',
  standalone: false,
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.scss',
})
export class UserListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  // Table configuration
  displayedColumns: string[] = [
    'fullName',
    'email',
    'roles',
    'isActive',
    'createdOn',
    'actions',
  ];
  dataSource = new MatTableDataSource<UserTableRow>([]);

  // Table columns configuration
  tableColumns: TableColumn[] = [
    { key: 'fullName', label: 'Full Name', sortable: true, width: '20%' },
    { key: 'email', label: 'Email', sortable: true, width: '25%' },
    { key: 'roles', label: 'Roles', sortable: false, width: '15%' },
    { key: 'isActive', label: 'Status', sortable: true, width: '10%' },
    { key: 'createdOn', label: 'Created On', sortable: true, width: '15%' },
    { key: 'actions', label: 'Actions', sortable: false, width: '15%' },
  ];

  // Pagination
  totalItems = 0;
  pageSize = 10;
  pageIndex = 0;
  pageSizeOptions = [5, 10, 25, 50];

  // Filtering and search
  searchControl = new FormControl('');
  statusFilter = new FormControl('all');
  roleFilter = new FormControl('');

  filterOptions: UserFilterOptions = {
    searchTerm: '',
    statusFilter: 'all',
    roleFilter: '',
    dateRange: { start: null, end: null },
  };

  // Sorting
  sortConfig: SortConfig = {
    field: 'createdOn',
    direction: 'desc',
  };

  // State management
  loadingStates: LoadingStates = {
    fetchingUsers: false,
    deletingUser: false,
    updatingUser: false,
    addingUser: false,
  };

  errorStates: ErrorStates = {
    fetchError: null,
    deleteError: null,
    updateError: null,
    addError: null,
  };

  // Modal states
  modalState: ModalState = 'closed';
  selectedUser: UserDetails | null = null;

  // User permissions
  currentUserRoles: string[] = [];
  hasAdminAccess = false;

  constructor(
    private userManagementService: UserManagementService,
    private userProfileService: UserProfileService,
    private notificationService: NotificationService,
    private injector: Injector,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
    this.setupSearchControls();
    this.loadUsers();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.userManagementService.clearStates();
  }

  private initializeComponent(): void {
    // Initialize user permissions
    this.userProfileService
      .hasAdminAccess()
      .pipe(takeUntil(this.destroy$))
      .subscribe((hasAccess) => {
        this.hasAdminAccess = hasAccess;
      });

    this.userProfileService
      .getUserRoles()
      .pipe(takeUntil(this.destroy$))
      .subscribe((roles) => {
        this.currentUserRoles = roles;
      });
  }

  private setupSubscriptions(): void {
    // Subscribe to loading states
    this.userManagementService.loadingStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.loadingStates = states;
      });

    // Subscribe to error states
    this.userManagementService.errorStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.errorStates = states;
        this.handleErrors(states);
      });

    // Subscribe to users data
    this.userManagementService.users$
      .pipe(takeUntil(this.destroy$))
      .subscribe((pagedResponse) => {
        if (pagedResponse) {
          this.totalItems = pagedResponse.totalItems;
          this.dataSource.data = this.transformUsersToTableRows(
            pagedResponse.items,
          );
        }
      });
  }

  private setupSearchControls(): void {
    // Setup search debouncing
    this.searchControl.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((searchTerm) => {
        this.filterOptions.searchTerm = searchTerm || '';
        this.resetPaginationAndLoad();
      });

    // Setup status filter
    this.statusFilter.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((status) => {
        this.filterOptions.statusFilter = status as
          | 'all'
          | 'active'
          | 'inactive';
        this.resetPaginationAndLoad();
      });

    // Setup role filter
    this.roleFilter.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((role) => {
        this.filterOptions.roleFilter = role || '';
        this.resetPaginationAndLoad();
      });
  }

  private loadUsers(): void {
    const params: PaginationParameters = {
      pageNumber: this.pageIndex + 1, // Backend uses 1-based indexing
      pageSize: this.pageSize,
      sortField: this.sortConfig.field,
      sortOrder: this.sortConfig.direction,
      name: this.filterOptions.searchTerm || undefined,
      status:
        this.filterOptions.statusFilter === 'all'
          ? undefined
          : this.filterOptions.statusFilter,
      role: this.filterOptions.roleFilter || undefined,
    };

    this.userManagementService.getUsers(params).subscribe({
      next: () => {
        // Data is handled in the subscription above
      },
      error: (error) => {
        console.error('Error loading users:', error);
      },
    });
  }

  private transformUsersToTableRows(users: UserDetails[]): UserTableRow[] {
    return users.map((user) => ({
      ...user,
      displayRoles: Array.isArray(user.roles)
        ? user.roles.join(', ')
        : (user.roles as string) || 'User',
      statusDisplay: user.isActive ? 'Active' : 'Inactive',
      formattedCreatedOn: this.formatDate(user.createdOn),
      actions: this.getUserActionPermissions(user),
    }));
  }

  private getUserActionPermissions(user: UserDetails): UserActionPermissions {
    const isSuperAdmin = this.currentUserRoles.some(
      (role) =>
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );
    const isAdmin = this.currentUserRoles.some(
      (role) => role.toLowerCase() === 'admin',
    );

    // Super Admin can do everything
    if (isSuperAdmin) {
      return {
        canView: true,
        canEdit: true,
        canToggleStatus: true,
      };
    }

    // Admin can view and edit, but limited status toggle permissions
    if (isAdmin) {
      const userIsSuperAdmin = user.roles?.some(
        (role) =>
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin',
      );

      return {
        canView: true,
        canEdit: !userIsSuperAdmin, // Can't edit super admins
        canToggleStatus: !userIsSuperAdmin, // Can't toggle status of super admins
      };
    }

    // Regular users have no permissions
    return {
      canView: false,
      canEdit: false,
      canToggleStatus: false,
    };
  }

  private formatDate(date: Date): string {
    if (!date) return 'N/A';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  private resetPaginationAndLoad(): void {
    this.pageIndex = 0;
    if (this.paginator) {
      this.paginator.pageIndex = 0;
    }
    this.loadUsers();
  }

  private handleErrors(errorStates: ErrorStates): void {
    if (errorStates.fetchError) {
      this.showError(`Failed to load users: ${errorStates.fetchError}`);
    }
    if (errorStates.deleteError) {
      this.showError(`Failed to delete user: ${errorStates.deleteError}`);
    }
    if (errorStates.updateError) {
      this.showError(`Failed to update user: ${errorStates.updateError}`);
    }
    if (errorStates.addError) {
      this.showError(`Failed to add user: ${errorStates.addError}`);
    }
  }

  private showError(message: string): void {
    this.notificationService.showError(message);
  }

  private showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  // Public methods for template
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadUsers();
  }

  onSortChange(sort: Sort): void {
    this.sortConfig = {
      field: sort.active,
      direction: sort.direction as 'asc' | 'desc',
    };
    this.resetPaginationAndLoad();
  }

  onViewUser(user: UserTableRow): void {
    this.selectedUser = user;
    this.modalState = 'view';
    // Navigate to user details page
    this.router.navigate(['/user-management/details', user.id]);
  }

  onEditUser(user: UserTableRow): void {
    this.selectedUser = user;
    this.modalState = 'edit';
    // Navigate to edit user page
    this.router.navigate(['/user-management/edit', user.id]);
  }

  onStatusChange(user: UserTableRow, newStatus: boolean): void {
    // Directly perform the status change without confirmation dialog
    this.performStatusToggle(user, newStatus);
  }

  private performStatusToggle(user: UserDetails, newStatus: boolean): void {
    if (!user.id) {
      this.showError('User ID is missing');
      return;
    }

    const actionText = newStatus ? 'activated' : 'deactivated';

    this.userManagementService
      .deleteUser(user.id, {
        isActive: newStatus,
      })
      .subscribe({
        next: () => {
          this.showSuccess(
            `User ${user.fullName || user.email} has been ${actionText} successfully`,
          );
          this.refreshUsers();
        },
        error: (error) => {
          console.error(`Error ${actionText.slice(0, -1)}ing user:`, error);
          this.showError(
            `Failed to ${actionText.slice(0, -1)} user: ${error.message}`,
          );
        },
      });
  }

  onAddUser(): void {
    this.selectedUser = null;
    this.modalState = 'add';
    // Navigate to add user page
    this.router.navigate(['/user-management/add']);
  }

  refreshUsers(): void {
    this.loadUsers();
  }

  resetFilters(): void {
    // Reset all form controls to their default values
    this.searchControl.setValue('');
    this.statusFilter.setValue('all');
    this.roleFilter.setValue('');

    // Reset filter options
    this.filterOptions = {
      searchTerm: '',
      statusFilter: 'all',
      roleFilter: '',
      dateRange: { start: null, end: null },
    };

    // Reset pagination and reload data
    this.resetPaginationAndLoad();
  }

  // Debug method to test authentication
  debugAuth(): void {
    console.log('=== USER MANAGEMENT DEBUG ===');

    // Access the authentication service through dependency injection
    const authService = this.injector.get(AuthenticationService);

    console.log('1. Token Analysis:');
    authService.debugToken();

    console.log('2. User Profile Service Analysis:');
    this.userProfileService.getUserRoles().subscribe((roles) => {
      console.log('  - Current user roles from UserProfileService:', roles);
    });

    this.userProfileService.hasAdminAccess().subscribe((hasAdmin) => {
      console.log('  - Has admin access:', hasAdmin);
    });

    console.log('3. Auth State Analysis:');
    const authStateService = this.injector.get(AuthStateService);
    authStateService.authState$.subscribe((state: any) => {
      console.log('  - Auth state:', state);
    });

    console.log('4. Direct API Test:');
    authService.debugApiCall();

    console.log('5. Component State:');
    console.log('  - Current user roles (component):', this.currentUserRoles);
    console.log('  - Has admin access (component):', this.hasAdminAccess);

    console.log('=== END DEBUG ===');
  }

  /**
   * Get profile picture URL for a user
   */
  getProfilePictureUrl(user: UserTableRow): string | undefined {
    if (!user.profileImageUrl) {
      return undefined;
    }

    // If it's already a full URL, return as is
    if (user.profileImageUrl.startsWith('http')) {
      return user.profileImageUrl;
    }

    // Otherwise, construct the full S3 URL using UserProfileService
    return this.userProfileService.getS3Url(user.profileImageUrl);
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(user: UserTableRow): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }
}
