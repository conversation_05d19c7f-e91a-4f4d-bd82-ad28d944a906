import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Material UI Modules
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';

import { MatTooltipModule } from '@angular/material/tooltip';

// ng-zorro-antd Modules
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzFormModule } from 'ng-zorro-antd/form';

// Layout Module
import { LayoutModule } from '../../Layout/layout.module';

// Routing
import { UserManagementRoutingModule } from './user-management-routing.module';

// Components
import { UserListComponent } from './Pages/user-list/user-list.component';
import { AddEditUserComponent } from './Pages/add-edit-user/add-edit-user.component';
import { UserDetailsComponent } from './Pages/user-details/user-details.component';

import { CardHeaderComponent } from './Components/card-header/card-header.component';

@NgModule({
  declarations: [
    UserListComponent,
    AddEditUserComponent,
    UserDetailsComponent,
    CardHeaderComponent,
  ],
  imports: [
    DatePipe,
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    UserManagementRoutingModule,
    LayoutModule,
    // Material UI Modules
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    // ng-zorro-antd Modules
    NzInputModule,
    NzSelectModule,
    NzButtonModule,
    NzIconModule,
    NzFormModule,
  ],
})
export class UserManagementModule {}
