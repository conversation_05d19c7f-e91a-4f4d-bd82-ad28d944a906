<div class="user-details-container">
  <!-- Header Section -->
  <!-- <div class="header-section">
    <div class="header-content">
      <div class="header-navigation">
        <button
          mat-icon-button
          class="back-btn"
          (click)="onBackToList()"
          matTooltip="Back to User List"
        >
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">User Details</h1>
          <p class="page-subtitle">View and manage user information</p>
        </div>
      </div>
      <div class="header-actions" *ngIf="user">
        <button
          mat-raised-button
          color="primary"
          class="edit-btn"
          (click)="onEditUser()"
        >
          <mat-icon>edit</mat-icon>
          Edit User
        </button>
      </div>
    </div>
  </div> -->

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p class="loading-text">Loading user details...</p>
  </div>

  <!-- User Details Content -->
  <div *ngIf="!isLoading && user" class="details-content">
    <!-- Form Section with Integrated Header -->
    <div class="form-section">
      <mat-card class="form-card">
        <!-- Reusable Card Header Component -->
        <app-card-header
          title="User Details"
          subtitle="View user information and permissions"
        >
          <!-- Action buttons projected into header -->
          <button
            type="button"
            mat-button
            class="cancel-btn"
            (click)="onBackToList()"
          >
            <mat-icon [style]="{ color: 'var(--text-primary)' }"
              >arrow_back</mat-icon
            >
            Back to List
          </button>
          <button
            type="button"
            mat-raised-button
            color="primary"
            class="edit-btn"
            (click)="onEditUser()"
          >
            <mat-icon>edit</mat-icon>
            Edit User
          </button>
        </app-card-header>

        <!-- Profile Picture Section -->
        <div class="form-section-group">
          <h3 class="section-title">Profile Picture</h3>
          <div class="profile-picture-container">
            <div
              class="profile-picture"
              [class.has-image]="getProfilePictureUrl(user)"
            >
              <img
                *ngIf="getProfilePictureUrl(user)"
                [src]="getProfilePictureUrl(user)"
                [alt]="user.fullName || 'User'"
                class="profile-image"
                #avatarImg
                (error)="avatarImg.style.display = 'none'"
              />
              <div
                *ngIf="!getProfilePictureUrl(user)"
                class="profile-placeholder"
              >
                <mat-icon class="placeholder-icon">person</mat-icon>
                <span class="initials">{{ getUserInitials(user) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Basic Information Section -->
        <div class="form-section-group">
          <h3 class="section-title">Basic Information</h3>
          <div class="form-row">
            <!-- Full Name -->
            <div class="display-field">
              <mat-icon class="field-icon">person</mat-icon>
              <div class="field-content">
                <label class="field-label">Full Name</label>
                <div class="field-value">{{ user.fullName || "N/A" }}</div>
              </div>
            </div>

            <!-- Email -->
            <div class="display-field">
              <mat-icon class="field-icon">email</mat-icon>
              <div class="field-content">
                <label class="field-label">Email Address</label>
                <div class="field-value">{{ user.email || "N/A" }}</div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <!-- Phone Number -->
            <div class="display-field">
              <mat-icon class="field-icon">phone</mat-icon>
              <div class="field-content">
                <label class="field-label">Phone Number</label>
                <div class="field-value">{{ user.phoneNumber || "N/A" }}</div>
              </div>
            </div>

            <!-- Status -->
            <div class="display-field">
              <mat-icon class="field-icon">toggle_on</mat-icon>
              <div class="field-content">
                <label class="field-label">Status</label>
                <div class="field-value">
                  <mat-chip
                    [class]="
                      user.isActive ? 'status-active' : 'status-inactive'
                    "
                  >
                    <mat-icon>{{
                      user.isActive ? "check_circle" : "cancel"
                    }}</mat-icon>
                    {{ user.isActive ? "Active" : "Inactive" }}
                  </mat-chip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Roles Section -->
        <div class="form-section-group">
          <h3 class="section-title">Roles & Permissions</h3>
          <div class="form-row">
            <div class="display-field full-width">
              <mat-icon class="field-icon">security</mat-icon>
              <div class="field-content">
                <label class="field-label">User Role</label>
                <div class="field-value">
                  <div class="roles-container">
                    <mat-chip
                      *ngFor="let role of user.roles"
                      [class]="getRoleClass(role)"
                    >
                      <mat-icon>{{ getRoleIcon(role) }}</mat-icon>
                      {{ getRoleDisplayName(role) }}
                    </mat-chip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Account Information Section -->
        <!-- <div class="form-section-group">
          <h3 class="section-title">Account Information</h3>
          <div class="form-row"> -->
        <!-- User ID -->
        <!-- <div class="display-field">
              <mat-icon class="field-icon">account_circle</mat-icon>
              <div class="field-content">
                <label class="field-label">User ID</label>
                <div class="field-value">{{ user.id || "N/A" }}</div>
              </div>
            </div> -->

        <!-- Created On -->
        <!-- <div class="display-field">
              <mat-icon class="field-icon">schedule</mat-icon>
              <div class="field-content">
                <label class="field-label">Created On</label>
                <div class="field-value">{{ formatDate(user.createdOn) }}</div>
              </div>
            </div>
          </div>
        </div> -->

        <!-- Description Section -->
        <div class="form-section-group" *ngIf="user.description">
          <h3 class="section-title">Additional Information</h3>
          <div class="form-row">
            <div class="display-field full-width">
              <mat-icon class="field-icon">description</mat-icon>
              <div class="field-content">
                <label class="field-label">Description</label>
                <div class="field-value description-text">
                  {{ user.description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action buttons moved to card header -->
      </mat-card>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !user" class="error-state">
    <mat-icon class="error-icon">error_outline</mat-icon>
    <h3>User Not Found</h3>
    <p>The requested user could not be found.</p>
    <button mat-raised-button color="primary" (click)="onBackToList()">
      Back to User List
    </button>
  </div>
</div>
