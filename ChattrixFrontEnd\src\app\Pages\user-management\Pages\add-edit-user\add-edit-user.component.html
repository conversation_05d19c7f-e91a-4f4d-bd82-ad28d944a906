<div class="add-edit-user-container">
  <!-- Form Section with Integrated Header -->
  <div class="form-section">
    <mat-card class="form-card">
      <!-- Reusable Card Header Component -->
      <app-card-header
        [title]="formTitle"
        [subtitle]="
          isEditMode
            ? 'Update user information and permissions'
            : 'Create a new user account with appropriate permissions'
        "
      >
        <!-- Action buttons projected into header -->
        <button
          type="button"
          mat-button
          class="cancel-btn"
          (click)="onCancel()"
        >
          Cancel
        </button>
        <button
          type="submit"
          mat-raised-button
          color="primary"
          class="submit-btn spinner-button"
          [disabled]="isLoading"
          (click)="onSubmit()"
        >
          <div class="button-content" [class.loading]="isLoading">
            <mat-spinner
              diameter="20"
              *ngIf="isLoading"
              class="button-spinner"
            ></mat-spinner>
            <div class="button-text" *ngIf="!isLoading">
              <mat-icon>{{ isEditMode ? "save" : "person_add" }}</mat-icon>
              <span>{{ submitButtonText }}</span>
            </div>
          </div>
        </button>
      </app-card-header>

      <form [formGroup]="userForm" (ngSubmit)="onSubmit()" novalidate>
        <!-- Basic Information Section -->
        <div class="form-section-group">
          <h3 class="section-title">Basic Information</h3>
          <div class="form-row">
            <!-- Full Name -->
            <mat-form-field class="form-field" appearance="outline">
              <mat-label>Full Name</mat-label>
              <input
                matInput
                formControlName="fullName"
                placeholder="Enter full name"
                autocomplete="name"
              />
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="userForm.get('fullName')?.hasError('required')">
                Full name is required
              </mat-error>
              <mat-error
                *ngIf="userForm.get('fullName')?.hasError('minlength')"
              >
                Full name must be at least 2 characters
              </mat-error>
            </mat-form-field>

            <!-- Email -->
            <mat-form-field class="form-field" appearance="outline">
              <mat-label>Email Address</mat-label>
              <input
                matInput
                *ngIf="!isEditMode || !userForm.get('email')?.disabled"
                formControlName="email"
                placeholder="Enter email address"
                type="email"
                autocomplete="email"
              />
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="userForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="userForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Phone Number -->
            <mat-form-field class="form-field" appearance="outline">
              <mat-label>Phone Number</mat-label>
              <input
                matInput
                formControlName="phoneNumber"
                placeholder="Enter phone number"
                type="tel"
                autocomplete="tel"
              />
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error
                *ngIf="userForm.get('phoneNumber')?.hasError('required')"
              >
                Phone number is required
              </mat-error>
              <mat-error
                *ngIf="userForm.get('phoneNumber')?.hasError('pattern')"
              >
                Phone number must be a valid number
              </mat-error>
              <mat-error
                *ngIf="
                  userForm.get('phoneNumber')?.hasError('minlength') ||
                  userForm.get('phoneNumber')?.hasError('maxlength')
                "
              >
                Phone number must be at least 10 digits long
              </mat-error>
            </mat-form-field>

            <!-- Status -->
            <!-- <mat-form-field class="form-field" appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select formControlName="isActive">
                <mat-option [value]="true">
                  <mat-icon>check_circle</mat-icon>
                  Active
                </mat-option>
                <mat-option [value]="false">
                  <mat-icon>cancel</mat-icon>
                  Inactive
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>toggle_on</mat-icon>
            </mat-form-field> -->
          </div>
        </div>

        <!-- Password Information Section (Add Mode Only) -->
        <!-- <div class="form-section-group" *ngIf="!isEditMode">
          <h3 class="section-title">Password Information</h3>
          <div class="password-info">
            <p class="info-text">
              <mat-icon class="info-icon">info</mat-icon>
              Default passwords will be assigned based on the selected role:
            </p>
            <ul class="password-list">
              <li><strong>User:</strong> User123&#64;</li>
              <li><strong>Admin:</strong> Admin123&#64;</li>
              <li><strong>Super Admin:</strong> Superadmin123&#64;</li>
            </ul>
            <p class="note-text">
              Users can change their password after first login.
            </p>
          </div>
        </div> -->

        <!-- Roles Section -->
        <div class="form-section-group">
          <h3 class="section-title">Roles & Permissions</h3>
          <div class="form-row">
            <mat-form-field class="form-field full-width" appearance="outline">
              <mat-label>User Role</mat-label>
              <mat-select formControlName="role">
                <mat-option
                  *ngFor="let role of availableRoles"
                  [value]="role.value"
                >
                  <mat-icon>{{ getRoleIcon(role.value) }}</mat-icon>
                  {{ role.label }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>security</mat-icon>
              <mat-error *ngIf="userForm.get('role')?.hasError('required')">
                Role is required
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Description Section -->
        <div class="form-section-group">
          <h3 class="section-title">Additional Information</h3>
          <div class="form-row">
            <mat-form-field class="form-field full-width" appearance="outline">
              <mat-label>Description</mat-label>
              <textarea
                matInput
                formControlName="description"
                placeholder="Enter user description or notes"
                rows="4"
                maxlength="50"
              ></textarea>
              <mat-icon matSuffix>description</mat-icon>
              <mat-hint>Optional description or notes about the user</mat-hint>
            </mat-form-field>
          </div>
        </div>

        <!-- Form actions moved to card header -->
      </form>
    </mat-card>
  </div>
</div>
